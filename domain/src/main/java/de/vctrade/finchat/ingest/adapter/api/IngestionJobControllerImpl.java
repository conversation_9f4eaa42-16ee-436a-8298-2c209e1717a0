package de.vctrade.finchat.ingest.adapter.api;

import de.vctrade.finchat.ingest.domain.model.ingest.IndexingStage;
import de.vctrade.finchat.ingest.domain.model.ingest.IngestionJob;
import de.vctrade.finchat.ingest.domain.repository.IngestionJobRepository;
import de.vctrade.finchat.ingest.adapter.model.IngestionJobResponse;
import de.vctrade.finchat.ingest.adapter.model.IngestionJobsResponse;
import de.vctrade.finchat.ingest.domain.service.IngestionJobService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.enums.ParameterIn;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

import java.util.UUID;

@Slf4j
@RestController
@RequestMapping("/ingestion-jobs")
@AllArgsConstructor
@Tag(name = "Ingestion Job API", description = "API for managing document ingestion jobs")
public class IngestionJobControllerImpl implements IngestionJobController {

    private final IngestionJobRepository ingestionJobRepository;
    private final IngestionJobService ingestionJobService;

    @Operation(
            operationId = "getIngestionJob",
            summary = "Get ingestion job status",
            description = "Retrieve status and details for a specific ingestion job by ID",
            responses = {
                    @ApiResponse(responseCode = "200", description = "Job found",
                            content = @Content(mediaType = "application/json", schema = @Schema(implementation = IngestionJobResponse.class))),
                    @ApiResponse(responseCode = "404", description = "Job not found")
            },
            security = {@SecurityRequirement(name = "OAuth2PasswordBearer")}
    )
    @GetMapping("/{id}")
    @Override
    public Mono<ResponseEntity<IngestionJobResponse>> getIngestionJob(
            @Parameter(name = "id", description = "Ingestion job ID", required = true, in = ParameterIn.PATH)
            @PathVariable("id") UUID id
    ) {
        return ingestionJobRepository.fetchById(id)
                .map(job -> ResponseEntity.ok().body(mapResponse(job)))
                .switchIfEmpty(Mono.error(new IllegalArgumentException("Job not found with ID: " + id)))
                .onErrorResume(IllegalArgumentException.class, error -> {
                    log.warn("Job not found: {}", error.getMessage());
                    return Mono.just(ResponseEntity.notFound().build());
                })
                .onErrorResume(error -> {
                    log.error("Cannot get ingestion job with ID: {}", id, error);
                    return Mono.just(ResponseEntity.internalServerError().build());
                });
    }

    @Operation(
            operationId = "listIngestionJobs",
            summary = "List ingestion jobs",
            description = "Retrieve a list of ingestion jobs with optional filtering",
            responses = {
                    @ApiResponse(responseCode = "200", description = "Jobs retrieved successfully",
                            content = @Content(mediaType = "application/json", schema = @Schema(implementation = IngestionJobsResponse.class)))
            },
            security = {@SecurityRequirement(name = "OAuth2PasswordBearer")}
    )
    @GetMapping
    @Override
    public Mono<ResponseEntity<IngestionJobsResponse>> listIngestionJobs(
            @Parameter(name = "status", description = "Filter by job status", in = ParameterIn.QUERY)
            @RequestParam(value = "stage", required = false) IndexingStage stage,
            @Parameter(name = "document_id", description = "Filter by document ID", in = ParameterIn.QUERY)
            @RequestParam(value = "document_id", required = false) UUID documentId
    ) {
        // Validate input parameters
        if (documentId != null && documentId.toString().trim().isEmpty()) {
            return Mono.error(new IllegalArgumentException("Document ID cannot be empty"));
        }

        final Flux<IngestionJob> jobs;

        if (stage != null) {
            jobs = ingestionJobRepository.fetchByStatus(stage)
                    .onErrorResume(IllegalArgumentException.class, e -> {
                        log.warn("Invalid job status: {}", stage);
                        return Flux.error(new IllegalArgumentException("Invalid job status: " + stage));
                    });
        } else if (documentId != null) {
            jobs = ingestionJobRepository.fetchByDocumentId(documentId);
        } else {
            jobs = ingestionJobRepository.fetchAll();
        }

        return jobs.map(this::mapResponse)
                .collectList()
                .map(it -> ResponseEntity.ok().body(new IngestionJobsResponse(it)))
                .onErrorResume(IllegalArgumentException.class, error -> {
                    log.warn("Invalid request parameters: {}", error.getMessage());
                    return Mono.just(ResponseEntity.badRequest().body(new IngestionJobsResponse()));
                })
                .onErrorResume(error -> {
                    log.error("Cannot list ingestion jobs", error);
                    return Mono.just(ResponseEntity.internalServerError().body(new IngestionJobsResponse()));
                });
    }

    @Operation(
            operationId = "retryIngestionJob",
            summary = "Retry a failed ingestion job",
            description = "Retry a failed ingestion job if retry limit not exceeded",
            responses = {
                    @ApiResponse(responseCode = "202", description = "Job retry started",
                            content = @Content(mediaType = "application/json", schema = @Schema(implementation = IngestionJobResponse.class))),
                    @ApiResponse(responseCode = "404", description = "Job not found"),
                    @ApiResponse(responseCode = "409", description = "Job cannot be retried")
            },
            security = {@SecurityRequirement(name = "OAuth2PasswordBearer")}
    )
    @PostMapping("/{id}/retry")
    @PreAuthorize("isAuthenticated() and !isAnonymous()")
    @Override
    public Mono<ResponseEntity<UUID>> retryIngestionJob(
            @Parameter(name = "id", description = "Ingestion job ID", required = true, in = ParameterIn.PATH)
            @PathVariable("id") UUID id
    ) {
        return ingestionJobService.getJob(id)
                .switchIfEmpty(Mono.error(new IllegalArgumentException("Job not found with ID: " + id)))
                .flatMap(job -> {
                    // Check if job can be retried based on business rules
                    if (job.getRetryCount() >= 3) { // TODO: make configurable
                        return Mono.error(new IllegalStateException("Maximum retry count exceeded for job: " + id));
                    }
                    if (job.getIndexingStage() != IndexingStage.FAILED) {
                        return Mono.error(new IllegalStateException("Job is not in FAILED state and cannot be retried: " + id));
                    }
                    return ingestionJobService.retryJob(id);
                })
                .then(Mono.just(ResponseEntity.status(HttpStatus.ACCEPTED).body(id)))
                .onErrorResume(IllegalArgumentException.class, error -> {
                    log.warn("Job not found for retry: {}", error.getMessage());
                    return Mono.just(ResponseEntity.notFound().build());
                })
                .onErrorResume(IllegalStateException.class, error -> {
                    log.warn("Cannot retry job due to business rules: {}", error.getMessage());
                    return Mono.just(ResponseEntity.status(HttpStatus.CONFLICT).build());
                })
                .onErrorResume(error -> {
                    log.error("Cannot retry ingestion job with ID: {}", id, error);
                    return Mono.just(ResponseEntity.internalServerError().build());
                });
    }

    @Operation(
            operationId = "cancelIngestionJob",
            summary = "Cancel a running ingestion job",
            description = "Cancel a running or pending ingestion job",
            responses = {
                    @ApiResponse(responseCode = "204", description = "Job cancelled successfully"),
                    @ApiResponse(responseCode = "404", description = "Job not found"),
                    @ApiResponse(responseCode = "409", description = "Job cannot be cancelled")
            },
            security = {@SecurityRequirement(name = "OAuth2PasswordBearer")}
    )
    @DeleteMapping("/{id}")
    @PreAuthorize("isAuthenticated() and !isAnonymous()")
    @Override
    public Mono<ResponseEntity<Object>> cancelIngestionJob(
            @Parameter(name = "id", description = "Ingestion job ID", required = true, in = ParameterIn.PATH)
            @PathVariable("id") UUID id
    ) {
        return ingestionJobService.getJob(id)
                .switchIfEmpty(Mono.error(new IllegalArgumentException("Job not found with ID: " + id)))
                .flatMap(job -> {
                    // Check if job can be cancelled based on business rules
                    if (job.getIndexingStage() == IndexingStage.COMPLETED) {
                        return Mono.error(new IllegalStateException("Cannot cancel completed job: " + id));
                    }
                    if (job.getIndexingStage() == IndexingStage.CANCELLED) {
                        return Mono.error(new IllegalStateException("Job is already cancelled: " + id));
                    }
                    return ingestionJobService.cancelJob(id);
                })
                .then(Mono.just(ResponseEntity.noContent().build()))
                .onErrorResume(IllegalArgumentException.class, error -> {
                    log.warn("Job not found for cancellation: {}", error.getMessage());
                    return Mono.just(ResponseEntity.notFound().build());
                })
                .onErrorResume(IllegalStateException.class, error -> {
                    log.warn("Cannot cancel job due to business rules: {}", error.getMessage());
                    return Mono.just(ResponseEntity.status(HttpStatus.CONFLICT).build());
                })
                .onErrorResume(error -> {
                    log.error("Cannot cancel ingestion job with ID: {}", id, error);
                    return Mono.just(ResponseEntity.internalServerError().build());
                });
    }

    @Operation(
            operationId = "getJobsByDocument",
            summary = "Get jobs for a specific document",
            description = "Retrieve all ingestion jobs for a specific document",
            responses = {
                    @ApiResponse(responseCode = "200", description = "Jobs retrieved successfully",
                            content = @Content(mediaType = "application/json", schema = @Schema(implementation = IngestionJobsResponse.class)))
            },
            security = {@SecurityRequirement(name = "OAuth2PasswordBearer")}
    )
    @GetMapping("/document/{documentId}")
    @Override
    public Mono<ResponseEntity<IngestionJobsResponse>> getJobsByDocument(
            @Parameter(name = "documentId", description = "Document ID", required = true, in = ParameterIn.PATH)
            @PathVariable("documentId") UUID documentId
    ) {
        // Validate input parameter
        if (documentId.toString().trim().isEmpty()) {
            return Mono.error(new IllegalArgumentException("Document ID cannot be empty"));
        }

        return ingestionJobRepository.fetchByDocumentId(documentId)
                .map(this::mapResponse)
                .collectList()
                .map(jobs -> ResponseEntity.ok().body(new IngestionJobsResponse(jobs)))
                .onErrorResume(IllegalArgumentException.class, error -> {
                    log.warn("Invalid document ID parameter: {}", error.getMessage());
                    return Mono.just(ResponseEntity.badRequest().body(new IngestionJobsResponse()));
                })
                .onErrorResume(error -> {
                    log.error("Cannot list ingestion jobs by document ID: {}", documentId, error);
                    return Mono.just(ResponseEntity.internalServerError().body(new IngestionJobsResponse()));
                });
    }

    private IngestionJobResponse mapResponse(IngestionJob job) {
        return new IngestionJobResponse(
                job.getId(),
                job.getDocumentId(),
                job.getIndexingStage().name(),
                job.getRetryCount(),
                -1,          // FIXME max retries, make configurable
                job.getCreatedAt().toEpochMilli()
        );
    }
}
