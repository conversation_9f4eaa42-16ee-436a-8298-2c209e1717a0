package de.vctrade.finchat.ingest.application.handler;

import de.vctrade.finchat.ingest.application.config.DocumentIngestionProperties;
import de.vctrade.finchat.ingest.domain.model.extraction.ExtractionResult;
import de.vctrade.finchat.ingest.domain.model.ingest.IngestionJob;
import de.vctrade.finchat.ingest.domain.service.TextExtractionService;
import de.vctrade.finchat.ingest.domain.service.ContentFetchingService;
import de.vctrade.finchat.ingest.domain.model.fetch.FetchRequest;
import de.vctrade.finchat.ingest.domain.model.fetch.FetchResult;
import de.vctrade.finchat.ingest.domain.service.IngestionJobService;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Mono;
import reactor.core.scheduler.Schedulers;

import java.io.InputStream;
import java.net.URI;
import java.time.Duration;
import java.time.Instant;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.UUID;

/**
 * Comprehensive integration service that orchestrates the complete document ingestion flow.
 * This service provides high-level operations that coordinate between all subsystems:
 * - Document upload and validation
 * - Content fetching from various sources
 * - Text extraction and processing
 * - Job management and persistence
 * - Error handling and retry logic
 */
@Slf4j
@AllArgsConstructor
@Service
public class DocumentIngestionIntegrationHandler {

    private final DocumentIngestionFacade ingestionFacade;
    private final ContentFetchingService contentFetchingService;
    private final TextExtractionService textExtractionService;
    private final IngestionJobService ingestionJobService;
    private final DocumentIngestionProperties properties;

    /**
     * Process a document from a given URI source.
     * This is the main entry point for document ingestion.
     *
     * @param documentId the document identifier
     * @param sourceUri the URI to fetch the document from
     * @return Mono with the created ingestion job
     */
    public Mono<IngestionJob> processDocumentFromUri(UUID documentId, URI sourceUri) {
        log.info("Starting complete document ingestion for document {} from URI: {}", documentId, sourceUri);

        return validateDocumentSource(sourceUri)
            .then(ingestionFacade.startDocumentIngestion(documentId))
            .doOnSuccess(job -> log.info("Document ingestion initiated with job ID: {}", job.getId()))
            .doOnError(error -> log.error("Failed to process document {} from URI {}: {}",
                documentId, sourceUri, error.getMessage()));
    }

    /**
     * Process a document from uploaded file bytes.
     *
     * @param documentId the document identifier
     * @param fileName the original file name
     * @param fileInputStream the file content
     * @param mimeType the MIME type of the file
     * @return Mono with extraction result
     */
    public Mono<ExtractionResult> processUploadedDocument(
            UUID documentId,
            String fileName,
            InputStream fileInputStream,
            String mimeType
    ) {
        log.info("Processing uploaded document {} (file: {})", documentId, fileName);

        return textExtractionService.extractText(fileInputStream, mimeType, fileName)
                .subscribeOn(Schedulers.boundedElastic())
                .timeout(Duration.ofMillis(properties.getTika().getParseTimeout()))
                .doOnSuccess(result -> {
                    //validateFileSize(result.getText().length());
                    log.info(
                            "Successfully extracted text from uploaded document {}: {} characters",
                            documentId,
                            result.getText().length()
                    );
                })
                .doOnError(error ->
                        log.error("Failed to process uploaded document {}: {}", documentId, error.getMessage())
                );
    }

    /**
     * Fetch and extract text from a document at the given URI.
     * This method handles the complete fetch-extract pipeline.
     *
     * @param sourceUri the URI to fetch from
     * @return Mono with extraction result
     */
    public Mono<ExtractionResult> fetchAndExtractDocument(URI sourceUri) {
        log.info("Fetching and extracting document from URI: {}", sourceUri);

        FetchRequest fetchRequest = FetchRequest.builder()
            .source(sourceUri)
            .maxSizeBytes(properties.getFileSize().getMaxDownloadSize())
            .timeoutMillis(properties.getHttp().getReadTimeoutMillis())
            .build();

        return contentFetchingService.fetchContent(fetchRequest)
            .flatMap(this::extractTextFromFetchResult)
            .doOnSuccess(result -> log.info("Successfully fetched and extracted document from {}: {} characters",
                sourceUri, result.getText().length()))
            .doOnError(error -> log.error("Failed to fetch and extract document from {}: {}",
                sourceUri, error.getMessage()));
    }

    /**
     * Get comprehensive status information for a document ingestion job.
     *
     * @param jobId the job identifier
     * @return Mono with job status information
     */
    public Mono<DocumentIngestionStatus> getIngestionStatus(UUID jobId) {
        return ingestionJobService.getJob(jobId)
            .map(this::buildIngestionStatus)
            .switchIfEmpty(Mono.error(new IllegalArgumentException("Job not found: " + jobId)));
    }

    /**
     * Retry a failed ingestion job with enhanced error handling.
     *
     * @param jobId the job to retry
     * @return Mono with updated job entity
     */
    public Mono<Void> retryIngestionWithValidation(UUID jobId) {
        return ingestionJobService.getJob(jobId)
            .switchIfEmpty(Mono.error(new IllegalArgumentException("Job not found: " + jobId)))
            .flatMap(job -> {
                if (job.getRetryCount() >= properties.getJob().getMaxRetryCount()) {
                    return Mono.error(new IllegalStateException("Maximum retry count exceeded for job: " + jobId));
                }
                return ingestionFacade.retryIngestionJob(jobId);
            });
    }

    /**
     * Get system health and performance metrics.
     *
     * @return Mono with system status
     */
    public Mono<SystemHealthStatus> getSystemHealth() {
        return ingestionFacade.getSystemStatus()
            .map(status -> new SystemHealthStatus(
                status.getActiveJobCount(),
                status.isHealthy(),
                calculateThreadPoolUtilization(),
                calculateAvgProcessingTime()
            ));
    }

    // Private helper methods

    private Mono<Void> validateDocumentSource(URI sourceUri) {
        return Mono.fromRunnable(() -> {
            if (sourceUri == null) {
                throw new IllegalArgumentException("Source URI cannot be null");
            }
            String scheme = sourceUri.getScheme();
            if (!"http".equals(scheme) && !"https".equals(scheme) && !"file".equals(scheme)) {
                throw new IllegalArgumentException("Unsupported URI scheme: " + scheme);
            }
        });
    }

    private Mono<Void> validateFileSize(long fileSize) {
        return Mono.fromRunnable(() -> {
            if (fileSize > properties.getFileSize().getMaxUploadSize()) {
                throw new IllegalArgumentException(
                    String.format("File size %d exceeds maximum allowed size %d",
                        fileSize, properties.getFileSize().getMaxUploadSize()));
            }
        });
    }

    private Mono<ExtractionResult> extractTextFromFetchResult(FetchResult fetchResult) {
        return textExtractionService
                .extractText(
                        fetchResult.getContentStream(),
                        fetchResult.getContentType(),
                        fetchResult.getSource().toString()
                )
                .subscribeOn(Schedulers.boundedElastic())
                .timeout(Duration.ofMillis(properties.getTika().getParseTimeout()));
    }

    private DocumentIngestionStatus buildIngestionStatus(IngestionJob job) {
        return new DocumentIngestionStatus(
                job.getId(),
                job.getDocumentId(),
                job.getIndexingStage().toString(),
                Instant.ofEpochMilli(job.getCreatedAt().toEpochMilli())
                        .atZone(ZoneId.systemDefault()) // FIXME use shared datetime converter service
                        .toLocalDateTime(),
                LocalDateTime.now(),
                job.getRetryCount(),
                job.getError()
        );
    }

    private double calculateThreadPoolUtilization() {
        // Placeholder - would integrate with actual thread pool metrics
        return 0.0;
    }

    private long calculateAvgProcessingTime() {
        // Placeholder - would integrate with actual job timing metrics
        return 0L;
    }

    /**
     * Data class for document ingestion status
     */
    public record DocumentIngestionStatus(
            UUID jobId,
            UUID documentId,
            String status,
            LocalDateTime createdAt,
            LocalDateTime updatedAt,
            int retryCount,
            String errorMessage
    ) {}

    /**
     * Data class for system health status
     */
    public record SystemHealthStatus(
            int activeJobs,
            boolean systemHealthy,
            double threadPoolUtilization,
            long avgJobProcessingTime
    ) {}
}
