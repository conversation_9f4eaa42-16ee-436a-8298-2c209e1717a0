package de.vctrade.finchat.ingest.adapter.service;

import de.vctrade.finchat.ingest.domain.model.extraction.ExtractionMetadata;
import de.vctrade.finchat.ingest.domain.model.extraction.ExtractionResult;
import de.vctrade.finchat.ingest.domain.model.extraction.TextChunk;
import de.vctrade.finchat.ingest.domain.service.TextExtractionException;
import de.vctrade.finchat.ingest.domain.service.TextExtractionService;
import de.vctrade.finchat.ingest.domain.service.UnsupportedContentTypeException;
import jakarta.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.tika.exception.TikaException;
import org.apache.tika.metadata.Metadata;
import org.apache.tika.metadata.TikaCoreProperties;
import org.apache.tika.parser.AutoDetectParser;
import org.apache.tika.parser.ParseContext;

import org.apache.tika.sax.BodyContentHandler;
import org.springframework.stereotype.Service;
import org.xml.sax.SAXException;
import reactor.core.publisher.Mono;
import reactor.core.scheduler.Schedulers;

import java.io.IOException;
import java.io.InputStream;
import java.text.DateFormat;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.*;
import java.util.regex.Pattern;

/**
 * Apache Tika-based implementation of text extraction service.
 * Supports PDF, DOCX, TXT, and other common document formats.
 */
@Slf4j
@AllArgsConstructor
@Service
public class TikaTextExtractionService implements TextExtractionService {

    private static final int DEFAULT_CHUNK_SIZE = 2000;
    private static final int DEFAULT_CHUNK_OVERLAP = 200;

    private static final Set<String> SUPPORTED_CONTENT_TYPES = Set.of(
            "application/pdf",
            "application/vnd.openxmlformats-officedocument.wordprocessingml.document", // DOCX
            "application/msword", // DOC
            "text/plain",
            "text/html",
            "text/xml",
            "application/xml",
            "text/csv",
            "application/rtf",
            "application/vnd.openxmlformats-officedocument.presentationml.presentation", // PPTX
            "application/vnd.ms-powerpoint", // PPT
            "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet", // XLSX
            "application/vnd.ms-excel" // XLS
    );

    private static final Pattern SENTENCE_BOUNDARY = Pattern.compile("(?<=[.!?])\\s+(?=[A-Z])");
    private static final Pattern PARAGRAPH_BOUNDARY = Pattern.compile("\n\\s*\n");

    @Override
    public @NotNull Mono<ExtractionResult> extractText(
            @NotNull InputStream inputStream,
            @NotNull String contentType,
            @NotNull String filename) {

        return Mono.fromCallable(() -> {
            log.debug("Starting text extraction for file: {} with content type: {}", filename, contentType);

            if (!supportsContentType(contentType)) {
                throw new UnsupportedContentTypeException(contentType);
            }

            try {
                return performExtraction(inputStream, contentType, filename);
            } catch (Exception e) {
                log.error("Text extraction failed for file: {}", filename, e);
                throw new TextExtractionException("Failed to extract text from document: " + filename, e);
            }
        })
        .subscribeOn(Schedulers.boundedElastic())
        .doOnSuccess(result -> log.info("Successfully extracted {} characters from {}",
                result.getText().length(), filename))
        .doOnError(error -> log.error("Text extraction failed for {}: {}", filename, error.getMessage()));
    }

    @Override
    public boolean supportsContentType(@NotNull String contentType) {
        return SUPPORTED_CONTENT_TYPES.contains(contentType.toLowerCase());
    }

    private ExtractionResult performExtraction(InputStream inputStream, String contentType, String filename)
            throws IOException, SAXException, TikaException {

        Metadata metadata = new Metadata();
        metadata.set(Metadata.CONTENT_TYPE, contentType);
        metadata.set(TikaCoreProperties.RESOURCE_NAME_KEY, filename);

        BodyContentHandler handler = new BodyContentHandler(-1); // No character limit
        ParseContext parseContext = new ParseContext();

        AutoDetectParser parser = new AutoDetectParser();
        parser.parse(inputStream, handler, metadata, parseContext);

        String extractedText = handler.toString().trim();

        if (extractedText.isEmpty()) {
            log.warn("No text content extracted from file: {}", filename);
        }

        ExtractionMetadata docMetadata = buildDocumentMetadata(metadata, filename, contentType);
        List<TextChunk> chunks = chunkText(extractedText);
        Map<String, String> properties = extractProperties(metadata);

        return ExtractionResult.builder()
                .text(extractedText)
                .chunks(chunks)
                .metadata(docMetadata)
                .properties(properties)
                .build();
    }

    private ExtractionMetadata buildDocumentMetadata(Metadata metadata, String filename, String contentType) {
        return ExtractionMetadata.builder()
                .filename(filename)
                .contentType(contentType)
                .title(metadata.get(TikaCoreProperties.TITLE))
                .author(metadata.get(TikaCoreProperties.CREATOR))
                .subject(metadata.get(TikaCoreProperties.SUBJECT))
                .creationDate(parseDate(metadata.get(TikaCoreProperties.CREATED)))
                .modificationDate(parseDate(metadata.get(TikaCoreProperties.MODIFIED)))
                .pageCount(parseInteger(metadata.get("xmpTPg:NPages"), 0))
                .language(metadata.get(TikaCoreProperties.LANGUAGE))
                .encoding(metadata.get(Metadata.CONTENT_ENCODING))
                .build();
    }

    private List<TextChunk> chunkText(String text) {
        if (text == null || text.isEmpty()) {
            return Collections.emptyList();
        }

        List<TextChunk> chunks = new ArrayList<>();

        // Split by paragraphs first for better semantic chunking
        String[] paragraphs = PARAGRAPH_BOUNDARY.split(text);

        StringBuilder currentChunk = new StringBuilder();
        int currentOffset = 0;
        int chunkStartOffset = 0;

        for (String paragraph : paragraphs) {
            if (paragraph.trim().isEmpty()) {
                currentOffset += paragraph.length() + 2; // Account for paragraph separator
                continue;
            }

            // If adding this paragraph would exceed chunk size, create a new chunk
            if (!currentChunk.isEmpty() &&
                currentChunk.length() + paragraph.length() > DEFAULT_CHUNK_SIZE) {

                chunks.add(createTextChunk(currentChunk.toString().trim(), chunkStartOffset, chunks.size() + 1));

                // Start new chunk with overlap if possible
                String overlap = getOverlapText(currentChunk.toString(), DEFAULT_CHUNK_OVERLAP);
                currentChunk = new StringBuilder(overlap);
                chunkStartOffset = currentOffset - overlap.length();
            }

            if (!currentChunk.isEmpty()) {
                currentChunk.append("\n\n");
            }
            currentChunk.append(paragraph);
            currentOffset += paragraph.length() + 2;
        }

        // Add final chunk if it has content
        if (!currentChunk.isEmpty()) {
            chunks.add(createTextChunk(currentChunk.toString().trim(), chunkStartOffset, chunks.size() + 1));
        }

        return chunks;
    }

    private TextChunk createTextChunk(String content, int startOffset, int chunkNumber) {
        return TextChunk.builder()
                .content(content)
                .pageNumber(0) // Page number detection would require more sophisticated parsing
                .section("Chunk " + chunkNumber)
                .startOffset(startOffset)
                .length(content.length())
                .build();
    }

    @SuppressWarnings("SameParameterValue")
    private String getOverlapText(String text, int overlapSize) {
        if (text.length() <= overlapSize) {
            return text;
        }

        String overlap = text.substring(text.length() - overlapSize);

        // Try to break at sentence boundary for better semantic overlap
        String[] sentences = SENTENCE_BOUNDARY.split(overlap);
        if (sentences.length > 1) {
            // Return the last complete sentence(s)
            StringBuilder result = new StringBuilder();
            for (int i = 1; i < sentences.length; i++) {
                if (!result.isEmpty()) {
                    result.append(" ");
                }
                result.append(sentences[i]);
            }
            return result.toString().trim();
        }

        return overlap;
    }

    private Map<String, String> extractProperties(Metadata metadata) {
        Map<String, String> properties = new HashMap<>();

        for (String name : metadata.names()) {
            String value = metadata.get(name);
            if (value != null && !value.trim().isEmpty()) {
                properties.put(name, value);
            }
        }

        return properties;
    }

    private LocalDateTime parseDate(String dateString) {
        if (dateString == null || dateString.trim().isEmpty()) {
            return null;
        }

        try {
            // Tika typically returns dates in ISO format or as Date objects
            Date date = DateFormat.getDateTimeInstance().parse(dateString);

            return LocalDateTime.ofInstant(date.toInstant(), ZoneId.systemDefault());
        } catch (Exception e) {
            log.debug("Could not parse date: {}", dateString);
            return null;
        }
    }

    @SuppressWarnings("SameParameterValue")
    private int parseInteger(String value, int defaultValue) {
        if (value == null || value.trim().isEmpty()) {
            return defaultValue;
        }

        try {
            return Integer.parseInt(value.trim());
        } catch (NumberFormatException e) {
            log.debug("Could not parse integer: {}", value);
            return defaultValue;
        }
    }
}
