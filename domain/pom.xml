<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>de.vctrade</groupId>
        <artifactId>finchat-service</artifactId>
        <version>${revision}${sha1}${changelist}</version>
    </parent>

    <artifactId>finchat-domain</artifactId>

    <properties>
        <maven.compiler.source>17</maven.compiler.source>
        <maven.compiler.target>17</maven.compiler.target>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <zenwave.version>1.3.2</zenwave.version>
        <openapi-generator-version>7.13.0</openapi-generator-version>
        <javax-servlet-version>3.0.1</javax-servlet-version>
        <org-openapitools-version>0.2.6</org-openapitools-version>
        <javax-annotation-version>1.3.2</javax-annotation-version>
        <swagger-annotation-version>2.2.7</swagger-annotation-version>
    </properties>

    <dependencies>
        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
            <version>${org.projectlombok.version}</version>
            <scope>compile</scope>
        </dependency>
        <dependency>
            <groupId>javax.annotation</groupId>
            <artifactId>javax.annotation-api</artifactId>
            <version>${javax-annotation-version}</version>
        </dependency>
        <dependency>
            <groupId>io.swagger.core.v3</groupId>
            <artifactId>swagger-annotations</artifactId>
            <version>${swagger-annotation-version}</version>
        </dependency>
        <dependency>
            <groupId>io.projectreactor</groupId>
            <artifactId>reactor-core</artifactId>
        </dependency>
        <dependency>
            <groupId>org.apache.tika</groupId>
            <artifactId>tika-core</artifactId>
        </dependency>
        <dependency>
            <groupId>org.apache.tika</groupId>
            <artifactId>tika-parsers-standard-package</artifactId>
        </dependency>
    </dependencies>

    <build>
        <plugins>
            <plugin>
                <groupId>org.openapitools</groupId>
                <artifactId>openapi-generator-maven-plugin</artifactId>
                <version>${openapi-generator-version}</version>
                <dependencies>
                    <dependency>
                        <groupId>org.projectlombok</groupId>
                        <artifactId>lombok</artifactId>
                        <version>${org.projectlombok.version}</version>
                    </dependency>
                </dependencies>
                <executions>
                    <execution>
                        <goals>
                            <goal>generate</goal>
                        </goals>
                        <configuration>
                            <inputSpec>${project.basedir}/src/main/resources/openapi.yaml</inputSpec>
                            <generatorName>spring</generatorName>
                            <generateApis>true</generateApis>
                            <generateApiTests>false</generateApiTests>
                            <generateSupportingFiles>true</generateSupportingFiles>
                            <generateModels>true</generateModels>
                            <generateModelTests>false</generateModelTests>
                            <generateModelDocumentation>false</generateModelDocumentation>
                            <supportingFilesToGenerate>ApiUtil.java</supportingFilesToGenerate>
                            <apiPackage>de.vctrade.finchat.domain.generated.api</apiPackage>
                            <modelPackage>de.vctrade.finchat.domain.generated.dto</modelPackage>
                            <configOptions>
                                <useJakartaEe>true</useJakartaEe>
                                <useSpringBoot3>true</useSpringBoot3>
                                <skipDefaultResponse>true</skipDefaultResponse>
                                <interfaceOnly>true</interfaceOnly>
                                <serializableModel>true</serializableModel>
                                <sourceFolder>src/main/java</sourceFolder>
                                <generateApiTests>false</generateApiTests>
                                <generateBuilders>true</generateBuilders>
                                <serializableModel>true</serializableModel>
                                <useBeanValidation>true</useBeanValidation>
                                <sourceFolder>src/main/java</sourceFolder>
                                <useLombok>false</useLombok>
                                <openApiNullable>false</openApiNullable>
                                <useLombokAnnotations>true</useLombokAnnotations>
                                <useLombok>true</useLombok>
                                <!--suppress UnresolvedMavenProperty
                                <additionalModelTypeAnnotations>
                                    @lombok.experimental.SuperBuilder
                                    @lombok.NoArgsConstructor
                                    @lombok.AllArgsConstructor
                                    @lombok.Data
                                    @lombok.EqualsAndHashCode
                                </additionalModelTypeAnnotations> -->
                            </configOptions>
                            <output>${project.basedir}</output>

                        </configuration>
                    </execution>
                </executions>
            </plugin>

            <plugin>
                <groupId>com.google.code.maven-replacer-plugin</groupId>
                <artifactId>replacer</artifactId>
                <version>1.5.3</version>
                <executions>
                    <execution>
                        <id>removeUnusedAnnotations</id>
                        <phase>process-resources</phase>
                        <goals>
                            <goal>replace</goal>
                        </goals>
                        <configuration>
                            <basedir>${project.basedir}</basedir>
                            <includes>
                                <include>src/main/java/de/vctrade/finchat/domain/generated/**/*.java</include>
                            </includes>
                            <replacements>
                                <replacement>
                                    <token>@Generated.*\)</token>
                                    <value />
                                </replacement>
                            </replacements>
                        </configuration>
                    </execution>

                    <execution>
                        <id>setRoleValidationForMessageRequest</id>
                        <phase>process-resources</phase>
                        <goals>
                            <goal>replace</goal>
                        </goals>
                        <configuration>
                            <basedir>${project.basedir}</basedir>
                            <includes>
                                <include>src/main/java/de/vctrade/finchat/domain/generated/dto/MessageRequest.java</include>
                            </includes>
                            <replacements>
                                <replacement>
                                    <token>private Role role = Role.USER;</token>
                                    <value>@ValidMessageRequestRole&#10;  private Role role = Role.USER;</value>
                                </replacement>
                                <replacement>
                                    <token>import de.vctrade.finchat.domain.generated.dto.ChatSettings;</token>
                                    <value>import de.vctrade.finchat.domain.validation.ValidMessageRequestRole;&#10;import de.vctrade.finchat.domain.generated.dto.ChatSettings;</value>
                                </replacement>
                            </replacements>
                        </configuration>
                    </execution>

                </executions>
            </plugin>

        </plugins>
    </build>

</project>